# AI合同审核系统 - 前端界面

基于 Vue 3 + TypeScript + Element Plus 构建的现代化前端应用。

## 🚀 技术栈

- **框架**: Vue 3.4+ (Composition API)
- **构建工具**: Vite 5.4+
- **语言**: TypeScript 5.5+
- **UI库**: Element Plus 2.8+
- **路由**: Vue Router 4.4+
- **状态管理**: Pinia 2.2+
- **HTTP客户端**: Axios 1.7+
- **代码规范**: ESLint + Prettier
- **测试**: Vitest + Cypress

## 📦 项目结构

```
web/
├── public/                 # 静态资源
├── src/
│   ├── components/        # 公共组件
│   │   └── Layout/       # 布局组件
│   ├── router/           # 路由配置
│   ├── store/            # 状态管理
│   │   └── modules/      # 状态模块
│   ├── style/            # 全局样式
│   ├── utils/            # 工具函数
│   ├── views/            # 页面组件
│   ├── App.vue           # 根组件
│   └── main.ts           # 入口文件
├── .env                  # 环境变量
├── .env.development      # 开发环境变量
├── .env.production       # 生产环境变量
├── package.json          # 项目配置
├── tsconfig.json         # TypeScript配置
└── vite.config.ts        # Vite配置
```

## 🛠️ 开发指南

### 环境要求

- Node.js >= 18.0.0
- npm >= 9.0.0

### 安装依赖

```bash
npm install
```

### 启动开发服务器

```bash
npm run dev
```

访问 http://localhost:3000

### 构建生产版本

```bash
npm run build
```

### 预览生产构建

```bash
npm run preview
```

### 代码检查

```bash
npm run lint
```

### 代码格式化

```bash
npm run format
```

### 运行单元测试

```bash
npm run test:unit
```

### 运行端到端测试

```bash
npm run test:e2e
```

## 🎯 功能特性

### 已实现功能

- ✅ **项目基础架构**: Vue 3 + Vite + TypeScript
- ✅ **UI组件库**: Element Plus 完整集成
- ✅ **路由系统**: Vue Router 4 配置
- ✅ **状态管理**: Pinia 状态管理
- ✅ **布局系统**: 响应式侧边栏布局
- ✅ **主题切换**: 明暗主题支持
- ✅ **代码规范**: ESLint + Prettier
- ✅ **开发工具**: 热重载、自动导入
- ✅ **工作台页面**: 数据统计和快速操作

### 开发中功能

- 🔄 **文档管理**: 文档上传和管理界面
- 🔄 **审核流程**: 审核任务管理界面
- 🔄 **审核报告**: 交互式报告展示
- 🔄 **系统配置**: 配置管理界面

## 🔧 配置说明

### 环境变量

- `VITE_APP_TITLE`: 应用标题
- `VITE_APP_VERSION`: 应用版本
- `VITE_API_BASE_URL`: API基础URL
- `VITE_DEBUG`: 调试模式开关

### 代理配置

开发环境下，API请求会自动代理到后端服务器 (http://localhost:8000)

### 自动导入

项目配置了自动导入功能：
- Vue 3 API (ref, reactive, computed 等)
- Vue Router API (useRouter, useRoute 等)
- Pinia API (defineStore, storeToRefs 等)
- Element Plus 组件

## 📱 响应式设计

- 支持桌面端和移动端
- 侧边栏可折叠
- 自适应布局

## 🎨 主题系统

- 明亮主题 (默认)
- 暗色主题
- 主题状态持久化

## 🔗 API集成

- 统一的HTTP客户端配置
- 请求/响应拦截器
- 错误处理机制
- 自动重试机制

## 📋 开发规范

### 组件命名

- 使用 PascalCase 命名组件文件
- 组件名称应该具有描述性

### 代码风格

- 使用 Composition API
- TypeScript 严格模式
- ESLint + Prettier 自动格式化

### 提交规范

- feat: 新功能
- fix: 修复bug
- docs: 文档更新
- style: 代码格式调整
- refactor: 代码重构
- test: 测试相关
- chore: 构建过程或辅助工具的变动

## 🚀 部署

### 构建优化

- 代码分割
- 资源压缩
- Tree Shaking
- 缓存优化

### 部署方式

1. **静态部署**: 构建后部署到静态服务器
2. **Docker部署**: 使用 Nginx 容器
3. **CDN部署**: 静态资源CDN加速

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

MIT License