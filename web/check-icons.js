const icons = require('@element-plus/icons-vue');

const requiredIcons = [
  'UploadFilled', 'Document', 'Close', 'Loading', 'CircleCheck', 'CircleClose', 
  'VideoPlay', 'Delete', 'Search', 'Refresh', 'WarningFilled', 'ArrowDown', 
  'ArrowUp', 'Fold', 'Expand', 'Moon', 'Sunny', 'Odometer', 'Folder', 
  'DocumentChecked', 'DataAnalysis', 'Setting'
];

const missing = requiredIcons.filter(icon => !icons[icon]);

if (missing.length > 0) {
  console.log('Missing icons:', missing);
  console.log('\nAvailable similar icons:');
  const availableIcons = Object.keys(icons);
  missing.forEach(missingIcon => {
    const similar = availableIcons.filter(icon => 
      icon.toLowerCase().includes(missingIcon.toLowerCase()) ||
      missingIcon.toLowerCase().includes(icon.toLowerCase())
    );
    if (similar.length > 0) {
      console.log(`${missingIcon} -> ${similar.join(', ')}`);
    }
  });
} else {
  console.log('All icons are available');
}