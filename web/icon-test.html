<!DOCTYPE html>
<html>
<head>
    <title>图标测试</title>
    <script type="module">
        import * as icons from '/node_modules/@element-plus/icons-vue/dist/index.mjs';
        
        const requiredIcons = [
          'UploadFilled', 'Document', 'Close', 'Loading', 'CircleCheck', 'CircleCloseFilled', 
          'CaretRight', 'Delete', 'Search', 'Refresh', 'WarningFilled', 'ArrowDown', 
          'ArrowUp', 'Fold', 'Expand', 'Moon', 'Sunny', 'Odometer', 'Folder', 
          'DocumentChecked', 'DataLine', 'Setting'
        ];
        
        console.log('Available icons:', Object.keys(icons));
        
        const missing = requiredIcons.filter(icon => !icons[icon]);
        
        if (missing.length > 0) {
          console.log('Missing icons:', missing);
        } else {
          console.log('All icons are available');
        }
        
        document.body.innerHTML = `
          <h1>图标测试结果</h1>
          <p>缺失的图标: ${missing.length > 0 ? missing.join(', ') : '无'}</p>
          <p>检查浏览器控制台查看详细信息</p>
        `;
    </script>
</head>
<body>
    <p>正在检查图标...</p>
</body>
</html>