{"name": "ai-contract-review-frontend", "version": "1.0.0", "description": "AI合同审核系统前端界面", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/", "test:unit": "vitest", "test:e2e": "cypress run"}, "dependencies": {"@element-plus/icons-vue": "^2.3.2", "axios": "^1.11.0", "dayjs": "^1.11.13", "element-plus": "^2.11.1", "lodash-es": "^4.17.21", "nprogress": "^0.2.0", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.5.0", "vue": "^3.5.20", "vue-router": "^4.5.1"}, "devDependencies": {"@types/lodash-es": "^4.17.12", "@types/node": "^22.5.0", "@types/nprogress": "^0.2.3", "@vitejs/plugin-vue": "^6.0.1", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.6.0", "@vue/test-utils": "^2.4.6", "cypress": "^15.0.0", "eslint": "^9.34.0", "eslint-plugin-vue": "^10.4.0", "jsdom": "^26.1.0", "prettier": "^3.3.3", "typescript": "^5.9.0", "unplugin-auto-import": "^20.1.0", "unplugin-vue-components": "^29.0.0", "vite": "^7.1.3", "vitest": "^3.2.4", "vue-tsc": "^3.0.6"}}