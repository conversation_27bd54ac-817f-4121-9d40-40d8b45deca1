<template>
  <div class="document-stats">
    <el-row :gutter="16">
      <el-col :span="6">
        <div class="stat-card total" @click="filterByStatus('')">
          <div class="stat-content">
            <div class="stat-number">{{ stats.total }}</div>
            <div class="stat-label">总文档数</div>
          </div>
          <div class="stat-icon">
            <el-icon><Document /></el-icon>
          </div>
        </div>
      </el-col>
      
      <el-col :span="6">
        <div class="stat-card processing" @click="filterByStatus('processing')">
          <div class="stat-content">
            <div class="stat-number">{{ stats.processing }}</div>
            <div class="stat-label">处理中</div>
          </div>
          <div class="stat-icon">
            <el-icon class="rotating"><Loading /></el-icon>
          </div>
        </div>
      </el-col>
      
      <el-col :span="6">
        <div class="stat-card completed" @click="filterByStatus('completed')">
          <div class="stat-content">
            <div class="stat-number">{{ stats.completed }}</div>
            <div class="stat-label">已完成</div>
          </div>
          <div class="stat-icon">
            <el-icon><CircleCheck /></el-icon>
          </div>
        </div>
      </el-col>
      
      <el-col :span="6">
        <div class="stat-card error" @click="filterByStatus('error')">
          <div class="stat-content">
            <div class="stat-number">{{ stats.error }}</div>
            <div class="stat-label">处理失败</div>
          </div>
          <div class="stat-icon">
            <el-icon><CircleCloseFilled /></el-icon>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Document, Loading, CircleCheck, CircleCloseFilled } from '@element-plus/icons-vue'

interface DocumentInfo {
  id: string
  filename: string
  status: string
  uploadTime: string
}

const props = defineProps<{
  documents: DocumentInfo[]
}>()

const emit = defineEmits<{
  filterChange: [status: string]
}>()

const stats = computed(() => {
  const total = props.documents.length
  const processing = props.documents.filter(doc => doc.status === 'processing').length
  const completed = props.documents.filter(doc => doc.status === 'completed').length
  const error = props.documents.filter(doc => doc.status === 'error').length

  return {
    total,
    processing,
    completed,
    error
  }
})

const filterByStatus = (status: string) => {
  emit('filterChange', status)
}
</script>

<style scoped>
.document-stats {
  margin-bottom: 20px;
}

.stat-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: var(--el-color-primary);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: 600;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: var(--el-text-color-regular);
}

.stat-icon {
  font-size: 32px;
  opacity: 0.8;
}

/* 不同状态的颜色 */
.stat-card.total .stat-number,
.stat-card.total .stat-icon {
  color: var(--el-color-primary);
}

.stat-card.processing .stat-number,
.stat-card.processing .stat-icon {
  color: var(--el-color-warning);
}

.stat-card.completed .stat-number,
.stat-card.completed .stat-icon {
  color: var(--el-color-success);
}

.stat-card.error .stat-number,
.stat-card.error .stat-icon {
  color: var(--el-color-danger);
}

/* 旋转动画 */
.rotating {
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>