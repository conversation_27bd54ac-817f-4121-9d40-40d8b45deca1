<template>
  <div class="document-list">
    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-button
          v-if="selectedDocuments.length > 0"
          type="primary"
          :icon="CaretRight"
          @click="batchProcess"
          :loading="batchProcessing"
        >
          批量处理 ({{ selectedDocuments.length }})
        </el-button>
        <el-button
          v-if="selectedDocuments.length > 0"
          type="danger"
          :icon="Delete"
          @click="batchDelete"
          :loading="batchDeleting"
        >
          批量删除 ({{ selectedDocuments.length }})
        </el-button>
      </div>
      <div class="toolbar-right">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索文档名称"
          :prefix-icon="Search"
          clearable
          style="width: 200px"
        />
        <el-select v-model="statusFilter" placeholder="状态筛选" style="width: 120px; margin-left: 8px">
          <el-option label="全部" value="" />
          <el-option label="处理中" value="processing" />
          <el-option label="已完成" value="completed" />
          <el-option label="错误" value="error" />
        </el-select>
        <el-button :icon="Refresh" @click="refreshList" :loading="loading">刷新</el-button>
      </div>
    </div>

    <!-- 文档表格 -->
    <el-table
      :data="filteredDocuments"
      v-loading="loading"
      @selection-change="handleSelectionChange"
      stripe
      style="width: 100%"
    >
      <el-table-column type="selection" width="55" />
      
      <el-table-column prop="filename" label="文档名称" min-width="200">
        <template #default="{ row }">
          <div class="filename-cell">
            <el-icon class="file-icon">
              <Document />
            </el-icon>
            <span class="filename" :title="row.filename">{{ row.filename }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="size" label="文件大小" width="100">
        <template #default="{ row }">
          {{ formatFileSize(row.size) }}
        </template>
      </el-table-column>

      <el-table-column prop="status" label="状态" width="120">
        <template #default="{ row }">
          <el-tag :type="getStatusType(row.status)" size="small">
            {{ getStatusText(row.status) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="uploadTime" label="上传时间" width="160">
        <template #default="{ row }">
          {{ formatTime(row.uploadTime) }}
        </template>
      </el-table-column>

      <el-table-column prop="processTime" label="处理时间" width="160">
        <template #default="{ row }">
          {{ row.processTime ? formatTime(row.processTime) : '-' }}
        </template>
      </el-table-column>

      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <el-button
            v-if="row.status === 'completed'"
            type="primary"
            size="small"
            @click="viewReport(row)"
          >
            查看报告
          </el-button>
          <el-button
            v-if="row.status === 'uploading' || row.status === 'error'"
            type="success"
            size="small"
            @click="processDocument(row)"
            :loading="row.processing"
          >
            开始处理
          </el-button>
          <el-button
            type="info"
            size="small"
            @click="previewDocument(row)"
          >
            预览
          </el-button>
          <el-popconfirm
            title="确定删除这个文档吗？"
            @confirm="deleteDocument(row)"
          >
            <template #reference>
              <el-button type="danger" size="small">删除</el-button>
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 文档预览对话框 -->
    <el-dialog
      v-model="previewVisible"
      title="文档预览"
      width="80%"
      :before-close="closePreview"
    >
      <div class="preview-content">
        <div v-if="previewLoading" class="preview-loading">
          <el-icon class="is-loading"><Loading /></el-icon>
          <p>正在加载文档内容...</p>
        </div>
        <div v-else-if="previewError" class="preview-error">
          <el-icon><WarningFilled /></el-icon>
          <p>{{ previewError }}</p>
        </div>
        <div v-else class="preview-text">
          {{ previewContent }}
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Document,
  CaretRight,
  Delete,
  Search,
  Refresh,
  Loading,
  WarningFilled
} from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import * as documentApi from '@/api/document'
import type { DocumentInfo } from '@/api/document'

const router = useRouter()

// 响应式数据
const documents = ref<DocumentInfo[]>([])
const selectedDocuments = ref<DocumentInfo[]>([])
const loading = ref(false)
const batchProcessing = ref(false)
const batchDeleting = ref(false)
const searchKeyword = ref('')
const statusFilter = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 预览相关
const previewVisible = ref(false)
const previewLoading = ref(false)
const previewError = ref('')
const previewContent = ref('')
const currentPreviewDoc = ref<DocumentInfo | null>(null)

// 计算属性
const filteredDocuments = computed(() => {
  let filtered = documents.value

  // 搜索过滤
  if (searchKeyword.value) {
    filtered = filtered.filter(doc =>
      doc.filename.toLowerCase().includes(searchKeyword.value.toLowerCase())
    )
  }

  // 状态过滤
  if (statusFilter.value) {
    filtered = filtered.filter(doc => doc.status === statusFilter.value)
  }

  total.value = filtered.length
  
  // 分页
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filtered.slice(start, end)
})

// 生命周期
onMounted(() => {
  loadDocuments()
})

// 方法
const loadDocuments = async () => {
  loading.value = true
  try {
    const response = await documentApi.getDocumentList()
    documents.value = response.contracts || []
  } catch (error) {
    ElMessage.error('加载文档列表失败')
  } finally {
    loading.value = false
  }
}

const refreshList = () => {
  loadDocuments()
}

const handleSelectionChange = (selection: DocumentInfo[]) => {
  selectedDocuments.value = selection
}

const batchProcess = async () => {
  if (selectedDocuments.value.length === 0) return

  try {
    await ElMessageBox.confirm(
      `确定要处理选中的 ${selectedDocuments.value.length} 个文档吗？`,
      '批量处理',
      { type: 'warning' }
    )

    batchProcessing.value = true
    const ids = selectedDocuments.value.map(doc => doc.id)
    await documentApi.batchProcessDocuments(ids)
    
    ElMessage.success('批量处理任务已启动')
    selectedDocuments.value = []
    loadDocuments()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量处理失败')
    }
  } finally {
    batchProcessing.value = false
  }
}

const batchDelete = async () => {
  if (selectedDocuments.value.length === 0) return

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedDocuments.value.length} 个文档吗？此操作不可恢复！`,
      '批量删除',
      { type: 'warning' }
    )

    batchDeleting.value = true
    const ids = selectedDocuments.value.map(doc => doc.id)
    await documentApi.batchDeleteDocuments(ids)
    
    ElMessage.success('批量删除成功')
    selectedDocuments.value = []
    loadDocuments()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败')
    }
  } finally {
    batchDeleting.value = false
  }
}

const processDocument = async (doc: DocumentInfo) => {
  try {
    doc.processing = true
    await documentApi.processDocument(doc.id)
    ElMessage.success('处理任务已启动')
    loadDocuments()
  } catch (error) {
    ElMessage.error('启动处理失败')
  } finally {
    doc.processing = false
  }
}

const deleteDocument = async (doc: DocumentInfo) => {
  try {
    await documentApi.deleteDocument(doc.id)
    ElMessage.success('删除成功')
    loadDocuments()
  } catch (error) {
    ElMessage.error('删除失败')
  }
}

const viewReport = (doc: DocumentInfo) => {
  router.push(`/reports/${doc.id}`)
}

const previewDocument = async (doc: DocumentInfo) => {
  previewVisible.value = true
  previewLoading.value = true
  previewError.value = ''
  previewContent.value = ''
  currentPreviewDoc.value = doc

  try {
    const response = await documentApi.getDocumentDetail(doc.id)
    previewContent.value = response.content || '暂无预览内容'
  } catch (error) {
    previewError.value = '加载文档内容失败'
  } finally {
    previewLoading.value = false
  }
}

const closePreview = () => {
  previewVisible.value = false
  currentPreviewDoc.value = null
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
}

// 工具函数
const formatFileSize = (size: number) => {
  if (size < 1024) return `${size} B`
  if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)} KB`
  return `${(size / (1024 * 1024)).toFixed(1)} MB`
}

const formatTime = (time: string) => {
  return new Date(time).toLocaleString('zh-CN')
}

const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    uploading: 'info',
    processing: 'warning',
    completed: 'success',
    error: 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    uploading: '已上传',
    processing: '处理中',
    completed: '已完成',
    error: '处理失败'
  }
  return statusMap[status] || '未知'
}

// 设置状态筛选
const setStatusFilter = (status: string) => {
  statusFilter.value = status
}

// 暴露方法给父组件
defineExpose({
  refreshList: loadDocuments,
  setStatusFilter
})
</script>

<style scoped>
.document-list {
  background: var(--el-bg-color);
  border-radius: 8px;
  padding: 16px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.toolbar-left {
  display: flex;
  gap: 8px;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filename-cell {
  display: flex;
  align-items: center;
}

.file-icon {
  margin-right: 8px;
  color: var(--el-color-primary);
}

.filename {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.pagination {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.preview-content {
  min-height: 400px;
  max-height: 600px;
  overflow-y: auto;
}

.preview-loading,
.preview-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: var(--el-text-color-regular);
}

.preview-loading .el-icon {
  font-size: 32px;
  margin-bottom: 16px;
}

.preview-error .el-icon {
  font-size: 32px;
  margin-bottom: 16px;
  color: var(--el-color-warning);
}

.preview-text {
  padding: 16px;
  line-height: 1.6;
  white-space: pre-wrap;
  background: var(--el-fill-color-lighter);
  border-radius: 4px;
  font-family: 'Courier New', monospace;
}
</style>