<template>
  <div class="upload-area">
    <el-upload
      ref="uploadRef"
      class="upload-dragger"
      drag
      :http-request="customUpload"
      :multiple="true"
      :show-file-list="false"
      :before-upload="beforeUpload"
      :accept="acceptTypes"
    >
      <div class="upload-content">
        <el-icon class="upload-icon">
          <UploadFilled />
        </el-icon>
        <div class="upload-text">
          <p class="upload-title">点击或拖拽文件到此区域上传</p>
          <p class="upload-hint">支持 .doc, .docx, .pdf 格式，单个文件不超过 10MB</p>
        </div>
      </div>
    </el-upload>

    <!-- 上传进度 -->
    <div v-if="uploadingFiles.length > 0" class="upload-progress">
      <div v-for="file in uploadingFiles" :key="file.uid" class="progress-item">
        <div class="file-info">
          <el-icon><Document /></el-icon>
          <span class="filename">{{ file.name }}</span>
          <span class="file-size">{{ formatFileSize(file.size) }}</span>
        </div>
        <div class="progress-bar">
          <el-progress
            :percentage="file.progress"
            :status="file.status === 'error' ? 'exception' : undefined"
            :stroke-width="6"
          />
        </div>
        <el-button
          type="text"
          size="small"
          @click="cancelUpload(file.uid)"
          class="cancel-btn"
        >
          <el-icon><Close /></el-icon>
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { UploadFilled, Document, Close } from '@element-plus/icons-vue'
import type { UploadInstance, UploadRequestOptions } from 'element-plus'
import { uploadDocument } from '@/api/document'

interface UploadingFile {
  uid: string
  name: string
  size: number
  progress: number
  status: 'uploading' | 'success' | 'error'
}

const emit = defineEmits<{
  uploadSuccess: [file: any]
  uploadError: [error: any]
}>()

const uploadRef = ref<UploadInstance>()
const uploadingFiles = ref<UploadingFile[]>([])


const acceptTypes = '.doc,.docx,.pdf'
const maxFileSize = 10 * 1024 * 1024 // 10MB

// 上传前验证
const beforeUpload = (file: File) => {
  // 检查文件类型
  const validTypes = ['application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/pdf']
  if (!validTypes.includes(file.type) && !file.name.match(/\.(doc|docx|pdf)$/i)) {
    ElMessage.error('只能上传 Word 文档或 PDF 文件')
    return false
  }

  // 检查文件大小
  if (file.size > maxFileSize) {
    ElMessage.error('文件大小不能超过 10MB')
    return false
  }

  // 添加到上传列表
  const uploadingFile: UploadingFile = {
    uid: `${file.name}_${Date.now()}`,
    name: file.name,
    size: file.size,
    progress: 0,
    status: 'uploading'
  }
  uploadingFiles.value.push(uploadingFile)

  return true
}

// 自定义上传函数
const customUpload = async (options: UploadRequestOptions) => {
  const { file } = options
  
  try {
    const response = await uploadDocument(file, (progress) => {
      // 更新对应文件的进度
      const uploadingFile = uploadingFiles.value.find((f: UploadingFile) => f.name === file.name && f.size === file.size)
      if (uploadingFile) {
        uploadingFile.progress = progress
      }
    })
    
    // 上传成功
    const uploadingFile = uploadingFiles.value.find((f: UploadingFile) => f.name === file.name && f.size === file.size)
    if (uploadingFile) {
      uploadingFile.status = 'success'
      uploadingFile.progress = 100
    }
    
    ElMessage.success(`${file.name} 上传成功`)
    emit('uploadSuccess', response)
    
    // 延迟移除进度显示
    setTimeout(() => {
      removeUploadingFile(uploadingFile?.uid || '')
    }, 2000)
    
  } catch (error) {
    // 上传失败
    const uploadingFile = uploadingFiles.value.find((f: UploadingFile) => f.name === file.name && f.size === file.size)
    if (uploadingFile) {
      uploadingFile.status = 'error'
    }
    
    ElMessage.error(`${file.name} 上传失败`)
    emit('uploadError', error)
    
    // 延迟移除进度显示
    setTimeout(() => {
      removeUploadingFile(uploadingFile?.uid || '')
    }, 3000)
  }
}



// 取消上传
const cancelUpload = (uid: string) => {
  // 注意：使用自定义上传后，取消功能需要根据实际情况实现
  // 这里直接移除显示，实际的取消逻辑可能需要在 uploadDocument 中实现
  removeUploadingFile(uid)
}

// 移除上传文件
const removeUploadingFile = (uid: string) => {
  const index = uploadingFiles.value.findIndex((f: UploadingFile) => f.uid === uid)
  if (index > -1) {
    uploadingFiles.value.splice(index, 1)
  }
}

// 格式化文件大小
const formatFileSize = (size: number) => {
  if (size < 1024) return `${size} B`
  if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)} KB`
  return `${(size / (1024 * 1024)).toFixed(1)} MB`
}
</script>

<style scoped>
.upload-area {
  margin-bottom: 20px;
}

.upload-dragger {
  width: 100%;
}

.upload-dragger :deep(.el-upload-dragger) {
  width: 100%;
  height: 180px;
  border: 2px dashed var(--el-border-color);
  border-radius: 8px;
  background-color: var(--el-fill-color-lighter);
  transition: all 0.3s;
}

.upload-dragger :deep(.el-upload-dragger:hover) {
  border-color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.upload-icon {
  font-size: 48px;
  color: var(--el-color-primary);
  margin-bottom: 16px;
}

.upload-text {
  text-align: center;
}

.upload-title {
  font-size: 16px;
  color: var(--el-text-color-primary);
  margin: 0 0 8px 0;
}

.upload-hint {
  font-size: 14px;
  color: var(--el-text-color-regular);
  margin: 0;
}

.upload-progress {
  margin-top: 16px;
  border: 1px solid var(--el-border-color);
  border-radius: 8px;
  background-color: var(--el-bg-color);
}

.progress-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.progress-item:last-child {
  border-bottom: none;
}

.file-info {
  display: flex;
  align-items: center;
  min-width: 200px;
  margin-right: 16px;
}

.file-info .el-icon {
  margin-right: 8px;
  color: var(--el-color-primary);
}

.filename {
  font-weight: 500;
  margin-right: 8px;
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-size {
  font-size: 12px;
  color: var(--el-text-color-regular);
}

.progress-bar {
  flex: 1;
  margin-right: 16px;
}

.cancel-btn {
  color: var(--el-color-danger);
}
</style>