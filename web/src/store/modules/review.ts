import { defineStore } from 'pinia'

interface ReviewTask {
  id: string
  documentId: string
  documentName: string
  status: 'pending' | 'processing' | 'completed' | 'error'
  progress: number
  startTime: string
  endTime?: string
  results?: {
    elements: any[]
    clauses: any[]
    risks: any[]
  }
}

interface ReviewState {
  tasks: ReviewTask[]
  currentTask: ReviewTask | null
}

export const useReviewStore = defineStore('review', {
  state: (): ReviewState => ({
    tasks: [],
    currentTask: null
  }),

  getters: {
    taskList: (state) => state.tasks,
    pendingTasks: (state) => state.tasks.filter(task => task.status === 'pending'),
    processingTasks: (state) => state.tasks.filter(task => task.status === 'processing'),
    completedTasks: (state) => state.tasks.filter(task => task.status === 'completed')
  },

  actions: {
    addTask(task: ReviewTask) {
      this.tasks.unshift(task)
    },

    updateTask(id: string, updates: Partial<ReviewTask>) {
      const index = this.tasks.findIndex(task => task.id === id)
      if (index !== -1) {
        this.tasks[index] = { ...this.tasks[index], ...updates }
      }
    },

    setCurrentTask(task: ReviewTask | null) {
      this.currentTask = task
    },

    updateTaskProgress(id: string, progress: number) {
      this.updateTask(id, { progress })
    }
  }
})