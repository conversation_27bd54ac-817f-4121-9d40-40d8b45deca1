import { defineStore } from 'pinia'

interface ConfigState {
  elementConfig: any
  clauseConfig: any
  riskConfig: any
}

export const useConfigStore = defineStore('config', {
  state: (): ConfigState => ({
    elementConfig: null,
    clauseConfig: null,
    riskConfig: null
  }),

  getters: {
    hasElementConfig: (state) => !!state.elementConfig,
    hasClauseConfig: (state) => !!state.clauseConfig,
    hasRiskConfig: (state) => !!state.riskConfig
  },

  actions: {
    setElementConfig(config: any) {
      this.elementConfig = config
    },

    setClauseConfig(config: any) {
      this.clauseConfig = config
    },

    setRiskConfig(config: any) {
      this.riskConfig = config
    },

    async loadConfigs() {
      // 这里将来会调用API加载配置
      console.log('Loading configurations...')
    }
  }
})