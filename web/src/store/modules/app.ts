import { defineStore } from 'pinia'

interface AppState {
  loading: boolean
  collapsed: boolean
  theme: 'light' | 'dark'
  language: 'zh-CN' | 'en-US'
}

export const useAppStore = defineStore('app', {
  state: (): AppState => ({
    loading: false,
    collapsed: false,
    theme: 'light',
    language: 'zh-CN'
  }),

  getters: {
    isLoading: (state) => state.loading,
    isCollapsed: (state) => state.collapsed,
    currentTheme: (state) => state.theme,
    currentLanguage: (state) => state.language
  },

  actions: {
    setLoading(loading: boolean) {
      this.loading = loading
    },

    toggleSidebar() {
      this.collapsed = !this.collapsed
    },

    setTheme(theme: 'light' | 'dark') {
      this.theme = theme
      // 应用主题到document
      document.documentElement.className = theme === 'dark' ? 'dark' : ''
    },

    setLanguage(language: 'zh-CN' | 'en-US') {
      this.language = language
    }
  },

  persist: {
    key: 'app-store',
    storage: localStorage,
    pick: ['collapsed', 'theme', 'language']
  }
})