import { defineStore } from 'pinia'
/**
 * 文档信息
 */
interface Document {
  id: string
  name: string
  type: string
  size: number
  status: 'uploading' | 'processing' | 'completed' | 'error' // 文档状态
  uploadTime: string
  processTime?: string
}

/**
 * 文档状态
 */
interface DocumentState {
  documents: Document[]
  currentDocument: Document | null
  uploadProgress: number
}

/**
 * 文档Store
 */
export const useDocumentStore = defineStore('document', {
  state: (): DocumentState => ({
    documents: [],
    currentDocument: null,
    uploadProgress: 0
  }),

  getters: {
    documentList: (state) => state.documents,
    processingDocuments: (state) => state.documents.filter(doc => doc.status === 'processing'),
    completedDocuments: (state) => state.documents.filter(doc => doc.status === 'completed')
  },

  actions: {
    /**
     * 添加文档
     * @param document 文档信息
     */
    addDocument(document: Document) {
      this.documents.unshift(document)
    },
    /**
     * 更新文档
     * @param id 文档ID
     * @param updates 更新内容
     */
    updateDocument(id: string, updates: Partial<Document>) {
      const index = this.documents.findIndex(doc => doc.id === id)
      if (index !== -1) {
        this.documents[index] = { ...this.documents[index], ...updates }
      }
    },
    /**
     * 删除文档
     * @param id 文档ID
     */
    removeDocument(id: string) {
      const index = this.documents.findIndex(doc => doc.id === id)
      if (index !== -1) {
        this.documents.splice(index, 1)
      }
    },
    /**
     * 设置当前文档
     * @param document 文档信息
     */
    setCurrentDocument(document: Document | null) {
      this.currentDocument = document
    },
    /**
     * 设置上传进度
     * @param progress 上传进度
     */   
    setUploadProgress(progress: number) {
      this.uploadProgress = progress
    }
  }
})