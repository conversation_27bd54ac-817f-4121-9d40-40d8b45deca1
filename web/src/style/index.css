/* 全局样式 */
* {
  box-sizing: border-box;
}

html,
body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei',
    '微软雅黑', <PERSON><PERSON>, sans-serif;
}

#app {
  height: 100vh;
  overflow: hidden;
}

/* NProgress 样式自定义 */
#nprogress .bar {
  background: #409eff !important;
}

#nprogress .peg {
  box-shadow: 0 0 10px #409eff, 0 0 5px #409eff !important;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 暗色主题滚动条 */
.dark ::-webkit-scrollbar-track {
  background: #2c2c2c;
}

.dark ::-webkit-scrollbar-thumb {
  background: #6c6c6c;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: #8c8c8c;
}

/* 工具类 */
.flex {
  display: flex;
}

.flex-1 {
  flex: 1;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.text-center {
  text-align: center;
}

.full-height {
  height: 100vh;
}

.overflow-hidden {
  overflow: hidden;
}

.overflow-auto {
  overflow: auto;
}