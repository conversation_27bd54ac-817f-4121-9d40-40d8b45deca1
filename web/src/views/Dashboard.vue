<template>
  <div class="dashboard">
    <el-row :gutter="20">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-value">{{ documentStats.total }}</div>
            <div class="stat-label">总文档数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-value">{{ documentStats.processing }}</div>
            <div class="stat-label">处理中</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-value">{{ documentStats.completed }}</div>
            <div class="stat-label">已完成</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-value">{{ documentStats.error }}</div>
            <div class="stat-label">处理失败</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px">
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>最近处理的文档</span>
          </template>
          <el-table :data="recentDocuments" style="width: 100%">
            <el-table-column prop="name" label="文档名称" />
            <el-table-column prop="status" label="状态">
              <template #default="{ row }">
                <el-tag :type="getStatusType(row.status)">
                  {{ getStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="uploadTime" label="上传时间" />
          </el-table>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>快速操作</span>
          </template>
          <div class="quick-actions">
            <el-button type="primary" size="large" @click="$router.push('/documents')">
              <el-icon><Upload /></el-icon>
              上传文档
            </el-button>
            <el-button type="success" size="large" @click="$router.push('/review')">
              <el-icon><Document /></el-icon>
              查看审核
            </el-button>
            <el-button type="info" size="large" @click="$router.push('/reports')">
              <el-icon><DataAnalysis /></el-icon>
              审核报告
            </el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { useDocumentStore } from '@/store'
import { formatTime } from '@/utils'
import * as documentApi from '@/api/document'

const documentStore = useDocumentStore()
const documents = ref<any[]>([])

// 加载真实数据
onMounted(async () => {
  try {
    const response = await documentApi.getDocumentList()
    documents.value = response.contracts || []
  } catch (error) {
    console.error('加载文档数据失败:', error)
    documents.value = []
  }
})

const documentStats = computed(() => {
  const docs = documentStore.documentList
  return {
    total: docs.length,
    processing: docs.filter(d => d.status === 'processing').length,
    completed: docs.filter(d => d.status === 'completed').length,
    error: docs.filter(d => d.status === 'error').length
  }
})

const recentDocuments = computed(() => {
  return documentStore.documentList
    .slice(0, 5)
    .map(doc => ({
      ...doc,
      uploadTime: formatTime(doc.uploadTime, 'MM-DD HH:mm')
    }))
})

const getStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    uploading: 'info',
    processing: 'warning',
    completed: 'success',
    error: 'danger'
  }
  return typeMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    uploading: '上传中',
    processing: '处理中',
    completed: '已完成',
    error: '处理失败'
  }
  return textMap[status] || status
}
</script>

<style scoped>
.dashboard {
  padding: 20px;
}

.stat-card {
  text-align: center;
}

.stat-item {
  padding: 20px 0;
}

.stat-value {
  font-size: 32px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.quick-actions .el-button {
  justify-content: flex-start;
}
</style>