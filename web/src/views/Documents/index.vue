<template>
  <div class="documents-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">文档管理</h1>
        <p class="page-description">上传合同文档，管理审核任务，查看处理进度</p>
      </div>
      <DocumentStats :documents="documents" @filter-change="handleFilterChange" />
    </div>

    <!-- 上传区域 -->
    <el-card class="upload-section" shadow="never">
      <template #header>
        <div class="section-header">
          <h3>文档上传</h3>
          <el-button type="text" @click="toggleUploadArea">
            {{ showUpload ? '收起' : '展开' }}
            <el-icon><ArrowDown v-if="!showUpload" /><ArrowUp v-else /></el-icon>
          </el-button>
        </div>
      </template>
      <el-collapse-transition>
        <div v-show="showUpload">
          <UploadArea @upload-success="handleUploadSuccess" @upload-error="handleUploadError" />
        </div>
      </el-collapse-transition>
    </el-card>

    <!-- 文档列表 -->
    <el-card class="list-section" shadow="never">
      <template #header>
        <div class="section-header">
          <h3>文档列表</h3>
          <div class="header-actions">
            <el-button :icon="Refresh" @click="refreshDocuments">刷新</el-button>
          </div>
        </div>
      </template>
      <DocumentList ref="documentListRef" />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  ArrowDown,
  ArrowUp,
  Refresh
} from '@element-plus/icons-vue'
import UploadArea from '@/components/Documents/UploadArea.vue'
import DocumentList from '@/components/Documents/DocumentList.vue'
import DocumentStats from '@/components/Documents/DocumentStats.vue'
import { useDocumentStore } from '@/store'
import * as documentApi from '@/api/document'

// Store
const documentStore = useDocumentStore()

// 响应式数据
const showUpload = ref(true)
const documentListRef = ref()
const documents = ref<any[]>([])

const handleFilterChange = (status: string) => {
  // 将筛选状态传递给文档列表组件
  documentListRef.value?.setStatusFilter(status)
}

// 生命周期
onMounted(() => {
  loadDocuments()
})

// 方法
const loadDocuments = async () => {
  try {
    const response = await documentApi.getDocumentList()
    documents.value = response.contracts || []
  } catch (error) {
    ElMessage.error('加载文档统计失败')
  }
}

const toggleUploadArea = () => {
  showUpload.value = !showUpload.value
}

const handleUploadSuccess = (response: any) => {
  ElMessage.success('文档上传成功')
  refreshDocuments()
  loadDocuments() // 更新统计数据
}

const handleUploadError = (error: any) => {
  ElMessage.error('文档上传失败')
}

const refreshDocuments = () => {
  documentListRef.value?.refreshList()
  loadDocuments() // 更新统计数据
}
</script>

<style scoped>
.documents-page {
  padding: 20px;
  background: var(--el-fill-color-lighter);
  min-height: calc(100vh - 60px);
}

.page-header {
  margin-bottom: 20px;
}

.header-content {
  margin-bottom: 16px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0 0 8px 0;
}

.page-description {
  font-size: 14px;
  color: var(--el-text-color-regular);
  margin: 0;
}

.header-stats {
  margin-bottom: 20px;
}



.upload-section,
.list-section {
  margin-bottom: 20px;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.upload-section :deep(.el-card__header),
.list-section :deep(.el-card__header) {
  background: var(--el-fill-color-lighter);
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.header-actions {
  display: flex;
  gap: 8px;
}

.list-section :deep(.el-card__body) {
  padding: 0;
}
</style>