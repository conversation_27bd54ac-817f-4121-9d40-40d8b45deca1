import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'

// 配置NProgress
NProgress.configure({ showSpinner: false })

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    component: () => import('@/components/Layout/index.vue'),
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/Dashboard.vue'),
        meta: {
          title: '工作台'
        }
      },
      {
        path: 'documents',
        name: 'Documents',
        component: () => import('@/views/Documents/index.vue'),
        meta: {
          title: '文档管理'
        }
      },
      {
        path: 'review',
        name: 'Review',
        component: () => import('@/views/Review/index.vue'),
        meta: {
          title: '审核流程'
        }
      },
      {
        path: 'reports',
        name: 'Reports',
        component: () => import('@/views/Reports/index.vue'),
        meta: {
          title: '审核报告'
        }
      },
      {
        path: 'config',
        name: 'Config',
        component: () => import('@/views/Config/index.vue'),
        meta: {
          title: '系统配置'
        }
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/NotFound.vue'),
    meta: {
      title: '页面未找到'
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, _from, next) => {
  NProgress.start()
  
  // 设置页面标题
  if (to.meta?.title) {
    document.title = `${to.meta.title} - AI合同审核系统`
  }
  
  next()
})

router.afterEach(() => {
  NProgress.done()
})

export default router